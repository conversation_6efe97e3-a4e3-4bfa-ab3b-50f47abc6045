import { Router } from 'express'
import validator from 'express-validator'
const { body } = validator
import { AuthController } from '@/controllers/AuthController'
import { authenticateToken } from '@/middlewares/auth'
import { validate } from '@/middlewares/validation'

const router = Router()

// 用户登录
router.post('/login', validate([
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间'),
  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符')
]), AuthController.login)

// 验证token
router.get('/verify', authenticateToken, AuthController.verify)

// 用户登出
router.post('/logout', authenticateToken, AuthController.logout)

// 获取当前用户信息
router.get('/me', authenticateToken, AuthController.getCurrentUser)

// 修改密码
router.post('/change-password', authenticateToken, validate([
  body('currentPassword')
    .notEmpty()
    .withMessage('当前密码不能为空'),
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('新密码长度至少6个字符')
    .custom((value: string, { req }: any) => {
      if (value === req.body.currentPassword) {
        throw new Error('新密码不能与当前密码相同')
      }
      return true
    })
]), AuthController.changePassword)

export default router
