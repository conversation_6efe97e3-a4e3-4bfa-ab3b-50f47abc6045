import { Router } from 'express'
import validator from 'express-validator'
const { query } = validator
import { SearchController } from '@/controllers/SearchController'
import { authenticateToken } from '@/middlewares/auth'
import { validate } from '@/middlewares/validation'

const router = Router()

// 所有路由都需要认证
router.use(authenticateToken)

// 全局搜索
router.get('/', validate([
  query('q')
    .notEmpty()
    .withMessage('搜索关键词不能为空')
    .isLength({ min: 1, max: 100 })
    .withMessage('搜索关键词长度必须在1-100个字符之间')
    .trim(),
  query('type')
    .optional()
    .isIn(['all', 'novels', 'records'])
    .withMessage('搜索类型必须是 all、novels 或 records'),
  query('categoryId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('分类ID必须是正整数')
    .toInt(),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数')
    .toInt(),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间')
    .toInt()
]), SearchController.globalSearch)

// 全文搜索
router.get('/fulltext', validate([
  query('q')
    .notEmpty()
    .withMessage('搜索关键词不能为空')
    .isLength({ min: 1, max: 100 })
    .withMessage('搜索关键词长度必须在1-100个字符之间')
    .trim(),
  query('type')
    .optional()
    .isIn(['all', 'novels', 'records'])
    .withMessage('搜索类型必须是 all、novels 或 records'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数')
    .toInt(),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间')
    .toInt()
]), SearchController.fullTextSearch)

// 获取搜索建议
router.get('/suggestions', validate([
  query('q')
    .notEmpty()
    .withMessage('搜索关键词不能为空')
    .isLength({ min: 2, max: 100 })
    .withMessage('搜索关键词长度必须在2-100个字符之间')
    .trim(),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 20 })
    .withMessage('建议数量必须在1-20之间')
    .toInt()
]), SearchController.getSearchSuggestions)

export default router
