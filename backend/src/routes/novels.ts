import { Router } from 'express'
import validator from 'express-validator'
const { body } = validator
import { NovelController } from '@/controllers/NovelController'
import { authenticateToken } from '@/middlewares/auth'
import { validate, validateId, validateSort, validatePagination } from '@/middlewares/validation'
import { ReadingStatus } from '@/entities/Novel'

const router = Router()

// 所有路由都需要认证
router.use(authenticateToken)

// 获取小说列表
router.get('/', 
  validatePagination,
  validateSort(['title', 'author', 'createdAt', 'updatedAt', 'personalRating']),
  NovelController.getNovels
)

// 获取小说详情
router.get('/:id', 
  validateId('id'),
  NovelController.getNovelById
)

// 创建小说
router.post('/', validate([
  body('categoryId')
    .isInt({ min: 1 })
    .withMessage('分类ID必须是正整数')
    .toInt(),
  body('title')
    .notEmpty()
    .withMessage('小说标题不能为空')
    .isLength({ min: 1, max: 200 })
    .withMessage('小说标题长度必须在1-200个字符之间')
    .trim(),
  body('author')
    .notEmpty()
    .withMessage('作者姓名不能为空')
    .isLength({ min: 1, max: 100 })
    .withMessage('作者姓名长度必须在1-100个字符之间')
    .trim(),
  body('description')
    .optional()
    .isLength({ max: 5000 })
    .withMessage('小说简介长度不能超过5000个字符')
    .trim(),
  body('coverImage')
    .optional()
    .isURL()
    .withMessage('封面图片必须是有效的URL'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('标签必须是数组格式'),
  body('tags.*')
    .optional()
    .isString()
    .withMessage('标签必须是字符串')
    .isLength({ min: 1, max: 50 })
    .withMessage('标签长度必须在1-50个字符之间')
    .trim(),
  body('readingStatus')
    .optional()
    .isIn(Object.values(ReadingStatus))
    .withMessage('阅读状态值无效'),
  body('personalRating')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('个人评分必须是1-5之间的整数')
    .toInt()
]), NovelController.createNovel)

// 更新小说
router.put('/:id', 
  validateId('id'),
  validate([
    body('categoryId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('分类ID必须是正整数')
      .toInt(),
    body('title')
      .optional()
      .notEmpty()
      .withMessage('小说标题不能为空')
      .isLength({ min: 1, max: 200 })
      .withMessage('小说标题长度必须在1-200个字符之间')
      .trim(),
    body('author')
      .optional()
      .notEmpty()
      .withMessage('作者姓名不能为空')
      .isLength({ min: 1, max: 100 })
      .withMessage('作者姓名长度必须在1-100个字符之间')
      .trim(),
    body('description')
      .optional()
      .isLength({ max: 5000 })
      .withMessage('小说简介长度不能超过5000个字符')
      .trim(),
    body('coverImage')
      .optional()
      .isURL()
      .withMessage('封面图片必须是有效的URL'),
    body('tags')
      .optional()
      .isArray()
      .withMessage('标签必须是数组格式'),
    body('tags.*')
      .optional()
      .isString()
      .withMessage('标签必须是字符串')
      .isLength({ min: 1, max: 50 })
      .withMessage('标签长度必须在1-50个字符之间')
      .trim(),
    body('readingStatus')
      .optional()
      .isIn(Object.values(ReadingStatus))
      .withMessage('阅读状态值无效'),
    body('personalRating')
      .optional()
      .isInt({ min: 1, max: 5 })
      .withMessage('个人评分必须是1-5之间的整数')
      .toInt()
  ]),
  NovelController.updateNovel
)

// 删除小说
router.delete('/:id', 
  validateId('id'),
  NovelController.deleteNovel
)

export default router
