import { Router } from 'express'
import validator from 'express-validator'
const { body } = validator
import { CategoryController } from '@/controllers/CategoryController'
import { authenticateToken } from '@/middlewares/auth'
import { validate, validateId, validateSort } from '@/middlewares/validation'

const router = Router()

// 所有路由都需要认证
router.use(authenticateToken)

// 获取分类列表
router.get('/', 
  validateSort(['name', 'sortOrder', 'createdAt']),
  CategoryController.getCategories
)

// 获取分类详情
router.get('/:id', 
  validateId('id'),
  CategoryController.getCategoryById
)

// 创建分类
router.post('/', validate([
  body('name')
    .notEmpty()
    .withMessage('分类名称不能为空')
    .isLength({ min: 1, max: 100 })
    .withMessage('分类名称长度必须在1-100个字符之间')
    .trim(),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('分类描述长度不能超过1000个字符')
    .trim(),
  body('sortOrder')
    .optional()
    .isInt({ min: 0 })
    .withMessage('排序权重必须是非负整数')
    .toInt()
]), CategoryController.createCategory)

// 更新分类
router.put('/:id', 
  validateId('id'),
  validate([
    body('name')
      .optional()
      .notEmpty()
      .withMessage('分类名称不能为空')
      .isLength({ min: 1, max: 100 })
      .withMessage('分类名称长度必须在1-100个字符之间')
      .trim(),
    body('description')
      .optional()
      .isLength({ max: 1000 })
      .withMessage('分类描述长度不能超过1000个字符')
      .trim(),
    body('sortOrder')
      .optional()
      .isInt({ min: 0 })
      .withMessage('排序权重必须是非负整数')
      .toInt()
  ]),
  CategoryController.updateCategory
)

// 删除分类
router.delete('/:id', 
  validateId('id'),
  CategoryController.deleteCategory
)

// 批量更新分类排序
router.patch('/order', validate([
  body('categories')
    .isArray({ min: 1 })
    .withMessage('分类列表不能为空'),
  body('categories.*.id')
    .isInt({ min: 1 })
    .withMessage('分类ID必须是正整数')
    .toInt(),
  body('categories.*.sortOrder')
    .isInt({ min: 0 })
    .withMessage('排序权重必须是非负整数')
    .toInt()
]), CategoryController.updateCategoriesOrder)

export default router
