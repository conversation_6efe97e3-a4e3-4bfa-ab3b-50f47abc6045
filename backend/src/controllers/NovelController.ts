import { Request, Response } from 'express'
import { AppDataSource } from '@/config/database'
import { Novel, ReadingStatus } from '@/entities/Novel'
import { Category } from '@/entities/Category'
import { AnalysisRecord } from '@/entities/AnalysisRecord'
import { successResponse, paginationResponse, ErrorResponses } from '@/utils/response'

export class NovelController {
  // 获取小说列表
  static async getNovels(req: Request, res: Response): Promise<void> {
    try {
      const {
        categoryId,
        search,
        status,
        sort = 'createdAt',
        order = 'desc'
      } = req.query
      
      const page = parseInt(req.query.page as string) || 1
      const limit = parseInt(req.query.size as string) || 20
      const offset = (page - 1) * limit
      
      const novelRepository = AppDataSource.getRepository(Novel)
      const queryBuilder = novelRepository
        .createQueryBuilder('novel')
        .leftJoinAndSelect('novel.category', 'category')
        .where('novel.isDeleted = 0')

      // 分类筛选
      if (categoryId) {
        queryBuilder.andWhere('novel.categoryId = :categoryId', { categoryId })
      }

      // 搜索
      if (search) {
        queryBuilder.andWhere(
          '(novel.title LIKE :search OR novel.author LIKE :search OR novel.description LIKE :search)',
          { search: `%${search}%` }
        )
      }

      // 状态筛选
      if (status) {
        queryBuilder.andWhere('novel.readingStatus = :status', { status })
      }

      // 排序
      const orderDirection = order === 'desc' ? 'DESC' : 'ASC'
      queryBuilder.orderBy(`novel.${sort}`, orderDirection)

      // 分页
      queryBuilder.skip(offset).take(limit)

      const [novels, total] = await queryBuilder.getManyAndCount()

      // 处理标签
      const novelsWithTags = novels.map(novel => ({
        ...novel,
        tags: novel.getTagsArray()
      }))

      paginationResponse(res, novelsWithTags, total, page, limit, '获取小说列表成功')
    } catch (error) {
      console.error('获取小说列表失败:', error)
      ErrorResponses.internalError(res, '获取小说列表失败')
    }
  }

  // 获取小说详情
  static async getNovelById(req: Request, res: Response): Promise<void> {
    try {
      const novelId = parseInt(req.params.id || '0')
      
      const novelRepository = AppDataSource.getRepository(Novel)
      const novel = await novelRepository
        .createQueryBuilder('novel')
        .leftJoinAndSelect('novel.category', 'category')
        .leftJoinAndSelect('novel.analysisRecords', 'record', 'record.isDeleted = 0')
        .where('novel.id = :id AND novel.isDeleted = 0', { id: novelId })
        .getOne()

      if (!novel) {
        ErrorResponses.notFound(res, '小说不存在')
        return
      }

      // 处理标签和分析记录
      const novelWithDetails = {
        ...novel,
        tags: novel.getTagsArray(),
        analysisRecords: novel.analysisRecords?.map(record => ({
          ...record,
          tags: record.getTagsArray()
        })) || []
      }

      successResponse(res, novelWithDetails, '获取小说详情成功')
    } catch (error) {
      console.error('获取小说详情失败:', error)
      ErrorResponses.internalError(res, '获取小说详情失败')
    }
  }

  // 创建小说
  static async createNovel(req: Request, res: Response): Promise<void> {
    try {
      const {
        categoryId,
        title,
        author,
        description,
        coverImage,
        tags = [],
        readingStatus = ReadingStatus.NOT_STARTED,
        personalRating
      } = req.body
      
      const novelRepository = AppDataSource.getRepository(Novel)
      const categoryRepository = AppDataSource.getRepository(Category)
      
      // 检查分类是否存在
      const category = await categoryRepository.findOne({
        where: { id: categoryId, isDeleted: 0 }
      })

      if (!category) {
        ErrorResponses.badRequest(res, '指定的分类不存在')
        return
      }

      // 检查同分类下标题是否重复
      const existingNovel = await novelRepository.findOne({
        where: { categoryId, title, isDeleted: 0 }
      })

      if (existingNovel) {
        ErrorResponses.conflict(res, '同分类下已存在相同标题的小说')
        return
      }

      // 创建小说
      const novel = novelRepository.create({
        categoryId,
        title,
        author,
        description,
        coverImage,
        readingStatus,
        personalRating,
        isDeleted: 0
      })

      // 设置标签
      novel.setTagsArray(tags)

      const savedNovel = await novelRepository.save(novel)

      // 获取完整信息返回
      const novelWithCategory = await novelRepository
        .createQueryBuilder('novel')
        .leftJoinAndSelect('novel.category', 'category')
        .where('novel.id = :id', { id: savedNovel.id })
        .getOne()

      successResponse(res, {
        ...novelWithCategory,
        tags: novelWithCategory!.getTagsArray()
      }, '创建小说成功', 201)
    } catch (error) {
      console.error('创建小说失败:', error)
      ErrorResponses.internalError(res, '创建小说失败')
    }
  }

  // 更新小说
  static async updateNovel(req: Request, res: Response): Promise<void> {
    try {
      const novelId = parseInt(req.params.id || '0')
      const {
        categoryId,
        title,
        author,
        description,
        coverImage,
        tags,
        readingStatus,
        personalRating
      } = req.body
      
      const novelRepository = AppDataSource.getRepository(Novel)
      const categoryRepository = AppDataSource.getRepository(Category)
      
      // 检查小说是否存在
      const novel = await novelRepository.findOne({
        where: { id: novelId, isDeleted: 0 }
      })

      if (!novel) {
        ErrorResponses.notFound(res, '小说不存在')
        return
      }

      // 如果修改了分类，检查新分类是否存在
      if (categoryId && categoryId !== novel.categoryId) {
        const category = await categoryRepository.findOne({
          where: { id: categoryId, isDeleted: 0 }
        })

        if (!category) {
          ErrorResponses.badRequest(res, '指定的分类不存在')
          return
        }
      }

      // 如果修改了标题，检查新标题是否重复
      if (title && title !== novel.title) {
        const targetCategoryId = categoryId || novel.categoryId
        const existingNovel = await novelRepository.findOne({
          where: { categoryId: targetCategoryId, title, isDeleted: 0 }
        })

        if (existingNovel) {
          ErrorResponses.conflict(res, '同分类下已存在相同标题的小说')
          return
        }
      }

      // 更新小说
      const updateData: Partial<Novel> = {}
      if (categoryId !== undefined) updateData.categoryId = categoryId
      if (title !== undefined) updateData.title = title
      if (author !== undefined) updateData.author = author
      if (description !== undefined) updateData.description = description
      if (coverImage !== undefined) updateData.coverImage = coverImage
      if (readingStatus !== undefined) updateData.readingStatus = readingStatus
      if (personalRating !== undefined) updateData.personalRating = personalRating

      // 处理标签
      if (tags !== undefined) {
        updateData.tags = JSON.stringify(tags)
      }

      await novelRepository.update(novelId, updateData)

      // 获取更新后的小说
      const updatedNovel = await novelRepository
        .createQueryBuilder('novel')
        .leftJoinAndSelect('novel.category', 'category')
        .where('novel.id = :id', { id: novelId })
        .getOne()

      successResponse(res, {
        ...updatedNovel,
        tags: updatedNovel!.getTagsArray()
      }, '更新小说成功')
    } catch (error) {
      console.error('更新小说失败:', error)
      ErrorResponses.internalError(res, '更新小说失败')
    }
  }

  // 删除小说
  static async deleteNovel(req: Request, res: Response): Promise<void> {
    try {
      const novelId = parseInt(req.params.id || '0')
      const { force = false } = req.query
      
      const novelRepository = AppDataSource.getRepository(Novel)
      const recordRepository = AppDataSource.getRepository(AnalysisRecord)
      
      // 检查小说是否存在
      const novel = await novelRepository.findOne({
        where: { id: novelId, isDeleted: 0 }
      })

      if (!novel) {
        ErrorResponses.notFound(res, '小说不存在')
        return
      }

      // 检查小说下是否有分析记录
      const recordCount = await recordRepository.count({
        where: { novelId, isDeleted: 0 }
      })

      if (recordCount > 0 && !force) {
        ErrorResponses.badRequest(res, '小说下还有分析记录，无法删除。如需强制删除，请添加 force=true 参数')
        return
      }

      // 如果强制删除，先删除小说下的所有分析记录
      if (force && recordCount > 0) {
        await recordRepository.update(
          { novelId, isDeleted: 0 },
          { isDeleted: 1 }
        )
      }

      // 软删除小说
      await novelRepository.update(novelId, { isDeleted: 1 })

      successResponse(res, null, '删除小说成功')
    } catch (error) {
      console.error('删除小说失败:', error)
      ErrorResponses.internalError(res, '删除小说失败')
    }
  }
}
