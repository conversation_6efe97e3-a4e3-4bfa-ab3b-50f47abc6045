import { Request, Response } from 'express'
import { AppDataSource } from '@/config/database'
import { Category } from '@/entities/Category'
import { Novel } from '@/entities/Novel'
import { successResponse, ErrorResponses } from '@/utils/response'

export class CategoryController {
  // 获取分类列表
  static async getCategories(req: Request, res: Response): Promise<void> {
    try {
      const { sort = 'sortOrder', order = 'asc' } = req.query
      
      const categoryRepository = AppDataSource.getRepository(Category)
      
      const categories = await categoryRepository
        .createQueryBuilder('category')
        .leftJoinAndSelect('category.novels', 'novel', 'novel.isDeleted = 0')
        .where('category.isDeleted = 0')
        .orderBy(`category.${sort}`, order === 'desc' ? 'DESC' : 'ASC')
        .getMany()

      // 计算每个分类的小说数量
      const categoriesWithCount = categories.map(category => ({
        ...category,
        novelsCount: category.novels?.length || 0,
        novels: undefined // 不返回具体的小说列表
      }))

      successResponse(res, categoriesWithCount, '获取分类列表成功')
    } catch (error) {
      console.error('获取分类列表失败:', error)
      ErrorResponses.internalError(res, '获取分类列表失败')
    }
  }

  // 获取分类详情
  static async getCategoryById(req: Request, res: Response): Promise<void> {
    try {
      const categoryId = parseInt(req.params.id || '0')
      
      const categoryRepository = AppDataSource.getRepository(Category)
      const category = await categoryRepository
        .createQueryBuilder('category')
        .leftJoinAndSelect('category.novels', 'novel', 'novel.isDeleted = 0')
        .where('category.id = :id AND category.isDeleted = 0', { id: categoryId })
        .getOne()

      if (!category) {
        ErrorResponses.notFound(res, '分类不存在')
        return
      }

      const categoryWithCount = {
        ...category,
        novelsCount: category.novels?.length || 0
      }

      successResponse(res, categoryWithCount, '获取分类详情成功')
    } catch (error) {
      console.error('获取分类详情失败:', error)
      ErrorResponses.internalError(res, '获取分类详情失败')
    }
  }

  // 创建分类
  static async createCategory(req: Request, res: Response): Promise<void> {
    try {
      const { name, description, sortOrder = 0 } = req.body
      
      const categoryRepository = AppDataSource.getRepository(Category)
      
      // 检查分类名称是否已存在
      const existingCategory = await categoryRepository.findOne({
        where: { name, isDeleted: 0 }
      })

      if (existingCategory) {
        ErrorResponses.conflict(res, '分类名称已存在')
        return
      }

      // 创建新分类
      const category = categoryRepository.create({
        name,
        description,
        sortOrder,
        isDeleted: 0
      })

      const savedCategory = await categoryRepository.save(category)

      successResponse(res, {
        ...savedCategory,
        novelsCount: 0
      }, '创建分类成功', 201)
    } catch (error) {
      console.error('创建分类失败:', error)
      ErrorResponses.internalError(res, '创建分类失败')
    }
  }

  // 更新分类
  static async updateCategory(req: Request, res: Response): Promise<void> {
    try {
      const categoryId = parseInt(req.params.id || '0')
      const { name, description, sortOrder } = req.body
      
      const categoryRepository = AppDataSource.getRepository(Category)
      
      // 检查分类是否存在
      const category = await categoryRepository.findOne({
        where: { id: categoryId, isDeleted: 0 }
      })

      if (!category) {
        ErrorResponses.notFound(res, '分类不存在')
        return
      }

      // 如果修改了名称，检查新名称是否已存在
      if (name && name !== category.name) {
        const existingCategory = await categoryRepository.findOne({
          where: { name, isDeleted: 0 }
        })

        if (existingCategory) {
          ErrorResponses.conflict(res, '分类名称已存在')
          return
        }
      }

      // 更新分类
      const updateData: Partial<Category> = {}
      if (name !== undefined) updateData.name = name
      if (description !== undefined) updateData.description = description
      if (sortOrder !== undefined) updateData.sortOrder = sortOrder

      await categoryRepository.update(categoryId, updateData)

      // 获取更新后的分类
      const updatedCategory = await categoryRepository
        .createQueryBuilder('category')
        .leftJoinAndSelect('category.novels', 'novel', 'novel.isDeleted = 0')
        .where('category.id = :id', { id: categoryId })
        .getOne()

      successResponse(res, {
        ...updatedCategory,
        novelsCount: updatedCategory?.novels?.length || 0,
        novels: undefined
      }, '更新分类成功')
    } catch (error) {
      console.error('更新分类失败:', error)
      ErrorResponses.internalError(res, '更新分类失败')
    }
  }

  // 删除分类
  static async deleteCategory(req: Request, res: Response): Promise<void> {
    try {
      const categoryId = parseInt(req.params.id || '0')
      const { force = false } = req.query
      
      const categoryRepository = AppDataSource.getRepository(Category)
      const novelRepository = AppDataSource.getRepository(Novel)
      
      // 检查分类是否存在
      const category = await categoryRepository.findOne({
        where: { id: categoryId, isDeleted: 0 }
      })

      if (!category) {
        ErrorResponses.notFound(res, '分类不存在')
        return
      }

      // 检查分类下是否有小说
      const novelCount = await novelRepository.count({
        where: { categoryId, isDeleted: 0 }
      })

      if (novelCount > 0 && !force) {
        ErrorResponses.badRequest(res, '分类下还有小说，无法删除。如需强制删除，请添加 force=true 参数')
        return
      }

      // 如果强制删除，先删除分类下的所有小说
      if (force && novelCount > 0) {
        await novelRepository.update(
          { categoryId, isDeleted: 0 },
          { isDeleted: 1 }
        )
      }

      // 软删除分类
      await categoryRepository.update(categoryId, { isDeleted: 1 })

      successResponse(res, null, '删除分类成功')
    } catch (error) {
      console.error('删除分类失败:', error)
      ErrorResponses.internalError(res, '删除分类失败')
    }
  }

  // 批量更新分类排序
  static async updateCategoriesOrder(req: Request, res: Response): Promise<void> {
    try {
      const { categories } = req.body // [{ id: 1, sortOrder: 1 }, ...]
      
      if (!Array.isArray(categories)) {
        ErrorResponses.badRequest(res, '参数格式错误')
        return
      }

      const categoryRepository = AppDataSource.getRepository(Category)
      
      // 批量更新排序
      const updatePromises = categories.map(({ id, sortOrder }) =>
        categoryRepository.update(
          { id, isDeleted: 0 },
          { sortOrder }
        )
      )

      await Promise.all(updatePromises)

      successResponse(res, null, '更新分类排序成功')
    } catch (error) {
      console.error('更新分类排序失败:', error)
      ErrorResponses.internalError(res, '更新分类排序失败')
    }
  }
}
