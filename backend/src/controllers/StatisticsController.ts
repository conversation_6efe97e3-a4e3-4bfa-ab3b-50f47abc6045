import { Request, Response } from 'express'
import { AppDataSource } from '@/config/database'
import { Category } from '@/entities/Category'
import { Novel } from '@/entities/Novel'
import { AnalysisRecord } from '@/entities/AnalysisRecord'
import { successResponse, ErrorResponses } from '@/utils/response'

export class StatisticsController {
  // 获取统计信息
  static async getStatistics(req: Request, res: Response): Promise<void> {
    try {
      const categoryRepository = AppDataSource.getRepository(Category)
      const novelRepository = AppDataSource.getRepository(Novel)
      const recordRepository = AppDataSource.getRepository(AnalysisRecord)

      // 基础统计
      const [
        categoriesCount,
        novelsCount,
        recordsCount,
        totalWordsResult
      ] = await Promise.all([
        categoryRepository.count({ where: { isDeleted: 0 } }),
        novelRepository.count({ where: { isDeleted: 0 } }),
        recordRepository.count({ where: { isDeleted: 0 } }),
        recordRepository
          .createQueryBuilder('record')
          .select('SUM(record.wordCount)', 'totalWords')
          .where('record.isDeleted = 0')
          .getRawOne()
      ])

      const totalWords = parseInt(totalWordsResult?.totalWords || '0')

      // 最近活动
      const recentActivity = await recordRepository
        .createQueryBuilder('record')
        .leftJoinAndSelect('record.novel', 'novel')
        .where('record.isDeleted = 0')
        .orderBy('record.createdAt', 'DESC')
        .take(10)
        .getMany()

      const recentActivityFormatted = recentActivity.map(record => ({
        type: 'record_created',
        title: '新增分析记录',
        recordTitle: record.title,
        novelTitle: record.novel.title,
        novelAuthor: record.novel.author,
        createdAt: record.createdAt
      }))

      // 阅读状态统计
      const readingStatusStats = await novelRepository
        .createQueryBuilder('novel')
        .select('novel.readingStatus', 'status')
        .addSelect('COUNT(*)', 'count')
        .where('novel.isDeleted = 0')
        .groupBy('novel.readingStatus')
        .getRawMany()

      // 分析类型统计
      const analysisTypeStats = await recordRepository
        .createQueryBuilder('record')
        .select('record.analysisType', 'type')
        .addSelect('COUNT(*)', 'count')
        .where('record.isDeleted = 0 AND record.analysisType IS NOT NULL')
        .groupBy('record.analysisType')
        .getRawMany()

      // 重要程度统计
      const importanceLevelStats = await recordRepository
        .createQueryBuilder('record')
        .select('record.importanceLevel', 'level')
        .addSelect('COUNT(*)', 'count')
        .where('record.isDeleted = 0')
        .groupBy('record.importanceLevel')
        .orderBy('record.importanceLevel', 'ASC')
        .getRawMany()

      // 分类统计
      const categoryStats = await categoryRepository
        .createQueryBuilder('category')
        .leftJoin('category.novels', 'novel', 'novel.isDeleted = 0')
        .select('category.id', 'id')
        .addSelect('category.name', 'name')
        .addSelect('COUNT(novel.id)', 'novelsCount')
        .where('category.isDeleted = 0')
        .groupBy('category.id')
        .orderBy('novelsCount', 'DESC')
        .getRawMany()

      // 月度统计（最近12个月）
      const monthlyStats = await recordRepository
        .createQueryBuilder('record')
        .select("strftime('%Y-%m', record.createdAt)", 'month')
        .addSelect('COUNT(*)', 'recordsCount')
        .addSelect('SUM(record.wordCount)', 'wordsCount')
        .where('record.isDeleted = 0')
        .andWhere("record.createdAt >= date('now', '-12 months')")
        .groupBy("strftime('%Y-%m', record.createdAt)")
        .orderBy('month', 'ASC')
        .getRawMany()

      const statistics = {
        overview: {
          categoriesCount,
          novelsCount,
          recordsCount,
          totalWords
        },
        recentActivity: recentActivityFormatted,
        readingStatus: readingStatusStats.map(stat => ({
          status: stat.status,
          count: parseInt(stat.count)
        })),
        analysisTypes: analysisTypeStats.map(stat => ({
          type: stat.type,
          count: parseInt(stat.count)
        })),
        importanceLevels: importanceLevelStats.map(stat => ({
          level: parseInt(stat.level),
          count: parseInt(stat.count)
        })),
        categories: categoryStats.map(stat => ({
          id: stat.id,
          name: stat.name,
          novelsCount: parseInt(stat.novelsCount)
        })),
        monthly: monthlyStats.map(stat => ({
          month: stat.month,
          recordsCount: parseInt(stat.recordsCount),
          wordsCount: parseInt(stat.wordsCount || '0')
        }))
      }

      successResponse(res, statistics, '获取统计信息成功')
    } catch (error) {
      console.error('获取统计信息失败:', error)
      ErrorResponses.internalError(res, '获取统计信息失败')
    }
  }

  // 获取分类详细统计
  static async getCategoryStatistics(req: Request, res: Response): Promise<void> {
    try {
      const categoryId = parseInt(req.params.id || '0')

      const categoryRepository = AppDataSource.getRepository(Category)
      const novelRepository = AppDataSource.getRepository(Novel)
      const recordRepository = AppDataSource.getRepository(AnalysisRecord)

      // 检查分类是否存在
      const category = await categoryRepository.findOne({
        where: { id: categoryId, isDeleted: 0 }
      })

      if (!category) {
        ErrorResponses.notFound(res, '分类不存在')
        return
      }

      // 分类下的小说统计
      const novelsStats = await novelRepository
        .createQueryBuilder('novel')
        .select('novel.readingStatus', 'status')
        .addSelect('COUNT(*)', 'count')
        .where('novel.categoryId = :categoryId AND novel.isDeleted = 0', { categoryId })
        .groupBy('novel.readingStatus')
        .getRawMany()

      // 分类下的分析记录统计
      const recordsStats = await recordRepository
        .createQueryBuilder('record')
        .leftJoin('record.novel', 'novel')
        .select('record.analysisType', 'type')
        .addSelect('COUNT(*)', 'count')
        .addSelect('SUM(record.wordCount)', 'totalWords')
        .where('novel.categoryId = :categoryId AND record.isDeleted = 0', { categoryId })
        .andWhere('record.analysisType IS NOT NULL')
        .groupBy('record.analysisType')
        .getRawMany()

      // 最活跃的小说
      const topNovels = await novelRepository
        .createQueryBuilder('novel')
        .select(['novel.id', 'novel.title', 'novel.author', 'novel.recordsCount'])
        .where('novel.categoryId = :categoryId AND novel.isDeleted = 0', { categoryId })
        .orderBy('novel.recordsCount', 'DESC')
        .take(10)
        .getMany()

      const categoryStatistics = {
        category: {
          id: category.id,
          name: category.name,
          description: category.description
        },
        novels: {
          total: novelsStats.reduce((sum, stat) => sum + parseInt(stat.count), 0),
          byStatus: novelsStats.map(stat => ({
            status: stat.status,
            count: parseInt(stat.count)
          }))
        },
        records: {
          total: recordsStats.reduce((sum, stat) => sum + parseInt(stat.count), 0),
          totalWords: recordsStats.reduce((sum, stat) => sum + parseInt(stat.totalWords || '0'), 0),
          byType: recordsStats.map(stat => ({
            type: stat.type,
            count: parseInt(stat.count),
            totalWords: parseInt(stat.totalWords || '0')
          }))
        },
        topNovels: topNovels.map(novel => ({
          id: novel.id,
          title: novel.title,
          author: novel.author,
          recordsCount: novel.recordsCount
        }))
      }

      successResponse(res, categoryStatistics, '获取分类统计信息成功')
    } catch (error) {
      console.error('获取分类统计信息失败:', error)
      ErrorResponses.internalError(res, '获取分类统计信息失败')
    }
  }

  // 获取小说详细统计
  static async getNovelStatistics(req: Request, res: Response): Promise<void> {
    try {
      const novelId = parseInt(req.params.id || '0')

      const novelRepository = AppDataSource.getRepository(Novel)
      const recordRepository = AppDataSource.getRepository(AnalysisRecord)

      // 检查小说是否存在
      const novel = await novelRepository
        .createQueryBuilder('novel')
        .leftJoinAndSelect('novel.category', 'category')
        .where('novel.id = :id AND novel.isDeleted = 0', { id: novelId })
        .getOne()

      if (!novel) {
        ErrorResponses.notFound(res, '小说不存在')
        return
      }

      // 分析记录统计
      const recordsStats = await recordRepository
        .createQueryBuilder('record')
        .select('record.analysisType', 'type')
        .addSelect('COUNT(*)', 'count')
        .addSelect('SUM(record.wordCount)', 'totalWords')
        .addSelect('AVG(record.importanceLevel)', 'avgImportance')
        .where('record.novelId = :novelId AND record.isDeleted = 0', { novelId })
        .andWhere('record.analysisType IS NOT NULL')
        .groupBy('record.analysisType')
        .getRawMany()

      // 重要程度分布
      const importanceStats = await recordRepository
        .createQueryBuilder('record')
        .select('record.importanceLevel', 'level')
        .addSelect('COUNT(*)', 'count')
        .where('record.novelId = :novelId AND record.isDeleted = 0', { novelId })
        .groupBy('record.importanceLevel')
        .orderBy('record.importanceLevel', 'ASC')
        .getRawMany()

      // 时间线统计（按月）
      const timelineStats = await recordRepository
        .createQueryBuilder('record')
        .select("strftime('%Y-%m', record.createdAt)", 'month')
        .addSelect('COUNT(*)', 'recordsCount')
        .addSelect('SUM(record.wordCount)', 'wordsCount')
        .where('record.novelId = :novelId AND record.isDeleted = 0', { novelId })
        .groupBy("strftime('%Y-%m', record.createdAt)")
        .orderBy('month', 'ASC')
        .getRawMany()

      const novelStatistics = {
        novel: {
          id: novel.id,
          title: novel.title,
          author: novel.author,
          readingStatus: novel.readingStatus,
          personalRating: novel.personalRating,
          category: {
            id: novel.category.id,
            name: novel.category.name
          }
        },
        records: {
          total: novel.recordsCount,
          totalWords: recordsStats.reduce((sum, stat) => sum + parseInt(stat.totalWords || '0'), 0),
          byType: recordsStats.map(stat => ({
            type: stat.type,
            count: parseInt(stat.count),
            totalWords: parseInt(stat.totalWords || '0'),
            avgImportance: parseFloat(stat.avgImportance || '0')
          })),
          byImportance: importanceStats.map(stat => ({
            level: parseInt(stat.level),
            count: parseInt(stat.count)
          }))
        },
        timeline: timelineStats.map(stat => ({
          month: stat.month,
          recordsCount: parseInt(stat.recordsCount),
          wordsCount: parseInt(stat.wordsCount || '0')
        }))
      }

      successResponse(res, novelStatistics, '获取小说统计信息成功')
    } catch (error) {
      console.error('获取小说统计信息失败:', error)
      ErrorResponses.internalError(res, '获取小说统计信息失败')
    }
  }
}
