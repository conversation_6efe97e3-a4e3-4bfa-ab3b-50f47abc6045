import jwt from 'jsonwebtoken'
import { appConfig } from '@/config'

export interface JwtPayload {
  userId: number
  username: string
  isAdmin: boolean
}

export interface TokenPair {
  token: string
  expiresIn: string
}

// 生成JWT token
export function generateToken(payload: JwtPayload): TokenPair {
  const token = jwt.sign(payload, appConfig.jwtSecret, {
    expiresIn: appConfig.jwtExpiresIn,
    issuer: appConfig.appName,
    audience: 'haowriter-client'
  } as jwt.SignOptions)

  return {
    token,
    expiresIn: appConfig.jwtExpiresIn
  }
}

// 验证JWT token
export function verifyToken(token: string): JwtPayload {
  try {
    const decoded = jwt.verify(token, appConfig.jwtSecret, {
      issuer: appConfig.appName,
      audience: 'haowriter-client'
    }) as JwtPayload

    return decoded
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('TOKEN_EXPIRED')
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('TOKEN_INVALID')
    } else {
      throw new Error('TOKEN_VERIFICATION_FAILED')
    }
  }
}

// 从请求头中提取token
export function extractTokenFromHeader(authHeader?: string): string | null {
  if (!authHeader) return null

  const parts = authHeader.split(' ')
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null
  }

  return parts[1] || null
}

// 解码token（不验证签名，用于获取过期token的信息）
export function decodeToken(token: string): JwtPayload | null {
  try {
    const decoded = jwt.decode(token) as JwtPayload
    return decoded
  } catch {
    return null
  }
}
