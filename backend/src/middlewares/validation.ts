import { Request, Response, NextFunction } from 'express'
import validator from 'express-validator'
const { validationResult } = validator
import type { ValidationChain } from 'express-validator'
import { ErrorResponses } from '@/utils/response'

// 验证结果处理中间件
export function handleValidationErrors(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const errors = validationResult(req)
  
  if (!errors.isEmpty()) {
    const details = errors.array().map(error => ({
      field: error.type === 'field' ? (error as any).path : 'unknown',
      message: error.msg,
      value: error.type === 'field' ? (error as any).value : undefined
    }))
    
    ErrorResponses.validationError(res, '参数验证失败', details)
    return
  }
  
  next()
}

// 创建验证中间件的辅助函数
export function validate(validations: ValidationChain[]) {
  return [
    ...validations,
    handleValidationErrors
  ]
}

// 分页参数验证
export function validatePagination(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const page = parseInt(req.query.page as string) || 1
  const limit = parseInt(req.query.limit as string) || 20
  
  if (page < 1) {
    ErrorResponses.badRequest(res, '页码必须大于0')
    return
  }
  
  if (limit < 1 || limit > 100) {
    ErrorResponses.badRequest(res, '每页数量必须在1-100之间')
    return
  }
  
  // 将验证后的分页参数添加到请求对象
  req.pagination = {
    page,
    limit,
    offset: (page - 1) * limit
  }
  
  next()
}

// 扩展Request接口
declare global {
  namespace Express {
    interface Request {
      pagination?: {
        page: number
        limit: number
        offset: number
      }
    }
  }
}

// ID参数验证
export function validateId(paramName = 'id') {
  return (req: Request, res: Response, next: NextFunction): void => {
    const id = parseInt(req.params[paramName])
    
    if (isNaN(id) || id < 1) {
      ErrorResponses.badRequest(res, `无效的${paramName}参数`)
      return
    }
    
    next()
  }
}

// 排序参数验证
export function validateSort(allowedFields: string[]) {
  return (req: Request, res: Response, next: NextFunction): void => {
    const sort = req.query.sort as string
    const order = req.query.order as string
    
    if (sort && !allowedFields.includes(sort)) {
      ErrorResponses.badRequest(res, `不支持的排序字段: ${sort}`)
      return
    }
    
    if (order && !['asc', 'desc'].includes(order.toLowerCase())) {
      ErrorResponses.badRequest(res, '排序方向必须是 asc 或 desc')
      return
    }
    
    next()
  }
}
