import 'reflect-metadata'
import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import morgan from 'morgan'
import { join } from 'path'
import { existsSync, mkdirSync } from 'fs'

import { appConfig } from '@/config'
import { initializeDatabase, closeDatabase } from '@/config/database'
import { AuthController } from '@/controllers/AuthController'
import { CategoryController } from '@/controllers/CategoryController'
import { NovelController } from '@/controllers/NovelController'
import { StatisticsController } from '@/controllers/StatisticsController'
import { authenticateToken } from '@/middlewares/auth'
import { ErrorResponses } from '@/utils/response'

const app = express()

// 创建必要的目录
const requiredDirs = [
  join(process.cwd(), 'data'),
  join(process.cwd(), 'uploads'),
  join(process.cwd(), 'logs'),
  join(process.cwd(), 'public')
]

requiredDirs.forEach(dir => {
  if (!existsSync(dir)) {
    mkdirSync(dir, { recursive: true })
    console.log(`创建目录: ${dir}`)
  }
})

// 安全中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false
}))

// CORS配置
app.use(cors({
  origin: appConfig.corsOrigin,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}))

// 压缩响应
app.use(compression())

// 请求日志
if (appConfig.isDevelopment) {
  app.use(morgan('dev'))
} else {
  app.use(morgan('combined'))
}

// 请求体解析
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// 静态文件服务
app.use('/uploads', express.static(join(process.cwd(), 'uploads')))
app.use(express.static(join(process.cwd(), 'public')))

// 健康检查端点
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'HaoWriter API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  })
})

// API文档端点
app.get('/api', (req, res) => {
  res.json({
    success: true,
    message: 'HaoWriter API',
    version: '1.0.0',
    endpoints: {
      auth: {
        login: 'POST /api/auth/login',
        verify: 'GET /api/auth/verify',
        me: 'GET /api/auth/me'
      },
      categories: {
        list: 'GET /api/categories',
        get: 'GET /api/categories/:id',
        create: 'POST /api/categories',
        update: 'PUT /api/categories/:id',
        delete: 'DELETE /api/categories/:id'
      },
      novels: {
        list: 'GET /api/novels',
        get: 'GET /api/novels/:id',
        create: 'POST /api/novels',
        update: 'PUT /api/novels/:id',
        delete: 'DELETE /api/novels/:id'
      },
      statistics: {
        overview: 'GET /api/statistics'
      }
    },
    timestamp: new Date().toISOString()
  })
})

// 认证路由
app.post('/api/auth/login', AuthController.login)
app.get('/api/auth/verify', authenticateToken, AuthController.verify)
app.get('/api/auth/me', authenticateToken, AuthController.getCurrentUser)
app.post('/api/auth/logout', authenticateToken, AuthController.logout)

// 分类路由
app.get('/api/categories', authenticateToken, CategoryController.getCategories)
app.get('/api/categories/:id', authenticateToken, CategoryController.getCategoryById)
app.post('/api/categories', authenticateToken, CategoryController.createCategory)
app.put('/api/categories/:id', authenticateToken, CategoryController.updateCategory)
app.delete('/api/categories/:id', authenticateToken, CategoryController.deleteCategory)

// 小说路由
app.get('/api/novels', authenticateToken, NovelController.getNovels)
app.get('/api/novels/:id', authenticateToken, NovelController.getNovelById)
app.post('/api/novels', authenticateToken, NovelController.createNovel)
app.put('/api/novels/:id', authenticateToken, NovelController.updateNovel)
app.delete('/api/novels/:id', authenticateToken, NovelController.deleteNovel)

// 统计路由
app.get('/api/statistics', authenticateToken, StatisticsController.getStatistics)

// SPA支持 - 所有未匹配的路由返回index.html
app.get('*', (req, res) => {
  const indexPath = join(process.cwd(), 'public', 'index.html')
  if (existsSync(indexPath)) {
    res.sendFile(indexPath)
  } else {
    res.status(404).json({
      success: false,
      error: {
        code: 'NOT_FOUND',
        message: '页面不存在'
      },
      timestamp: new Date().toISOString()
    })
  }
})

// 全局错误处理
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('未处理的错误:', error)
  
  if (res.headersSent) {
    return next(error)
  }
  
  ErrorResponses.internalError(res, '服务器内部错误')
})

// 404处理
app.use((req, res) => {
  ErrorResponses.notFound(res, '请求的资源不存在')
})

// 启动服务器
async function startServer() {
  try {
    // 初始化数据库
    await initializeDatabase()
    console.log('数据库初始化完成')
    
    // 启动HTTP服务器
    const server = app.listen(appConfig.port, () => {
      console.log(`
🚀 HaoWriter 简化版API服务器启动成功！

📍 环境: ${appConfig.nodeEnv}
🌐 地址: http://localhost:${appConfig.port}
📊 API文档: http://localhost:${appConfig.port}/api
🔍 健康检查: http://localhost:${appConfig.port}/api/health

${appConfig.isDevelopment ? '🔧 开发模式已启用' : '🏭 生产模式运行中'}
      `)
    })

    // 优雅关闭
    const gracefulShutdown = async (signal: string) => {
      console.log(`\n收到 ${signal} 信号，开始优雅关闭...`)
      
      server.close(async () => {
        console.log('HTTP 服务器已关闭')
        
        try {
          await closeDatabase()
          console.log('数据库连接已关闭')
          process.exit(0)
        } catch (error) {
          console.error('关闭数据库连接时出错:', error)
          process.exit(1)
        }
      })
      
      setTimeout(() => {
        console.error('强制关闭服务器')
        process.exit(1)
      }, 10000)
    }

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
    process.on('SIGINT', () => gracefulShutdown('SIGINT'))
    
  } catch (error) {
    console.error('启动服务器失败:', error)
    process.exit(1)
  }
}

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason, 'at:', promise)
  process.exit(1)
})

// 启动应用
startServer()
