{"name": "haowriter-backend", "version": "1.0.0", "description": "HaoWriter 小说分析平台后端服务", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node -r tsconfig-paths/register src/index.ts", "dev:simple": "nodemon --exec ts-node -r tsconfig-paths/register src/simple-server.ts", "build": "tsc", "start": "node dist/index.js", "db:init": "ts-node -r tsconfig-paths/register src/scripts/init-database.ts", "db:migrate": "ts-node -r tsconfig-paths/register src/scripts/migrate.ts", "user:create": "ts-node -r tsconfig-paths/register src/scripts/create-user.ts", "test": "jest", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "keywords": ["novel", "analysis", "management", "typescript", "express"], "author": "HaoWriter Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.2.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "marked": "^9.1.6", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "reflect-metadata": "^0.1.13", "sqlite3": "^5.1.6", "typeorm": "^0.3.17", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-validator": "^2.20.33", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}}