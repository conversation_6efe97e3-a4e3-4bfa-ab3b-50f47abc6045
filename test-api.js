// 简单的API测试脚本
const API_BASE = 'http://localhost:3000/api'

async function testAPI() {
  console.log('🧪 开始测试 HaoWriter API...\n')

  try {
    // 1. 测试健康检查
    console.log('1. 测试健康检查...')
    const healthResponse = await fetch(`${API_BASE}/health`)
    const healthData = await healthResponse.json()
    console.log('✅ 健康检查:', healthData.message)
    console.log('')

    // 2. 测试登录
    console.log('2. 测试用户登录...')
    const loginResponse = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123456'
      })
    })
    
    if (!loginResponse.ok) {
      throw new Error(`登录失败: ${loginResponse.status}`)
    }
    
    const loginData = await loginResponse.json()
    console.log('✅ 登录成功:', loginData.data.user.displayName)
    
    const token = loginData.data.token
    console.log('🔑 获取到Token')
    console.log('')

    // 3. 测试获取分类列表
    console.log('3. 测试获取分类列表...')
    const categoriesResponse = await fetch(`${API_BASE}/categories`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (!categoriesResponse.ok) {
      throw new Error(`获取分类失败: ${categoriesResponse.status}`)
    }
    
    const categoriesData = await categoriesResponse.json()
    console.log('✅ 分类列表获取成功:', `共 ${categoriesData.data.length} 个分类`)
    console.log('')

    // 4. 测试获取小说列表
    console.log('4. 测试获取小说列表...')
    const novelsResponse = await fetch(`${API_BASE}/novels`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (!novelsResponse.ok) {
      throw new Error(`获取小说失败: ${novelsResponse.status}`)
    }
    
    const novelsData = await novelsResponse.json()
    console.log('✅ 小说列表获取成功:', `共 ${novelsData.data.length} 本小说`)
    console.log('')

    // 5. 测试获取统计信息
    console.log('5. 测试获取统计信息...')
    const statsResponse = await fetch(`${API_BASE}/statistics`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (!statsResponse.ok) {
      throw new Error(`获取统计失败: ${statsResponse.status}`)
    }
    
    const statsData = await statsResponse.json()
    console.log('✅ 统计信息获取成功:')
    console.log(`   - 分类数量: ${statsData.data.overview.categoriesCount}`)
    console.log(`   - 小说数量: ${statsData.data.overview.novelsCount}`)
    console.log(`   - 记录数量: ${statsData.data.overview.recordsCount}`)
    console.log(`   - 总字数: ${statsData.data.overview.totalWords}`)
    console.log('')

    console.log('🎉 所有API测试通过！')
    
  } catch (error) {
    console.error('❌ API测试失败:', error.message)
  }
}

// 运行测试
testAPI()
