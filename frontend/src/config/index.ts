// 应用配置
export const appConfig = {
  // 应用信息
  name: 'HaoWriter',
  version: '1.0.0',
  description: '小说分析与拆解管理平台',

  // API配置
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || '/api',
  
  // 分页配置
  pagination: {
    defaultPageSize: 20,
    pageSizes: [10, 20, 50, 100]
  },

  // 文件上传配置
  upload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
  },

  // 主题配置
  theme: {
    primaryColor: '#409EFF',
    successColor: '#67C23A',
    warningColor: '#E6A23C',
    dangerColor: '#F56C6C',
    infoColor: '#909399'
  }
}

// 环境变量
export const env = {
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL,
  appTitle: import.meta.env.VITE_APP_TITLE || 'HaoWriter'
}
