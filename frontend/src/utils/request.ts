import axios, { AxiosError } from 'axios'
import type { AxiosResponse, AxiosRequestConfig } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { ApiResponse, ApiError } from '@/types/api'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加认证token
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    
    return config
  },
  (error: AxiosError) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response
    
    // API返回成功
    if (data.success) {
      return data
    } else {
      // API返回业务错误
      const errorMessage = data.message || '请求失败'
      ElMessage.error(errorMessage)
      return Promise.reject(new Error(errorMessage))
    }
  },
  async (error: AxiosError<ApiError>) => {
    const { response, message } = error
    
    if (response) {
      const { status, data } = response
      
      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          const authStore = useAuthStore()
          await authStore.logout()
          
          if (router.currentRoute.value.path !== '/login') {
            ElMessage.error('登录已过期，请重新登录')
            router.push('/login')
          }
          break
          
        case 403:
          ElMessage.error('权限不足')
          break
          
        case 404:
          ElMessage.error('请求的资源不存在')
          break
          
        case 422:
          // 参数验证错误，显示详细错误信息
          if (data?.error?.details && Array.isArray(data.error.details)) {
            const errorMessages = data.error.details.map(detail => detail.message).join('; ')
            ElMessage.error(errorMessages)
          } else {
            ElMessage.error(data?.error?.message || '参数验证失败')
          }
          break
          
        case 429:
          ElMessage.error('请求过于频繁，请稍后再试')
          break
          
        case 500:
          ElMessage.error('服务器内部错误')
          break
          
        default:
          const errorMessage = data?.error?.message || `请求失败 (${status})`
          ElMessage.error(errorMessage)
      }
    } else if (message.includes('timeout')) {
      ElMessage.error('请求超时，请检查网络连接')
    } else if (message.includes('Network Error')) {
      ElMessage.error('网络连接失败，请检查网络')
    } else {
      ElMessage.error('请求失败，请稍后重试')
    }
    
    return Promise.reject(error)
  }
)

// 请求方法封装
export const http = {
  get: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return request.get(url, { params })
  },
  
  post: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return request.post(url, data)
  },
  
  put: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return request.put(url, data)
  },
  
  patch: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return request.patch(url, data)
  },
  
  delete: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return request.delete(url, { params })
  }
}

// 文件上传
export const uploadFile = async (file: File, onProgress?: (progress: number) => void): Promise<string> => {
  const formData = new FormData()
  formData.append('file', file)
  
  try {
    const response = await request.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total && onProgress) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      }
    })
    
    return response.data.url
  } catch (error) {
    console.error('文件上传失败:', error)
    throw error
  }
}

// 确认对话框
export const confirmAction = async (message: string, title = '确认操作'): Promise<boolean> => {
  try {
    await ElMessageBox.confirm(message, title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    return true
  } catch {
    return false
  }
}

export default request
