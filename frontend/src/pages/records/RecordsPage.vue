<template>
  <div class="records-page">
    <div class="page-header">
      <h1 class="page-title">分析记录</h1>
      <p class="page-subtitle">管理小说分析记录，记录阅读心得</p>
    </div>

    <el-card>
      <div class="coming-soon">
        <el-icon size="64" color="#409EFF"><EditPen /></el-icon>
        <h2>分析记录功能</h2>
        <p>此功能正在开发中，敬请期待...</p>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { EditPen } from '@element-plus/icons-vue'
</script>

<style scoped>
.records-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #909399;
  margin: 0;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
}

.coming-soon h2 {
  margin: 20px 0 12px 0;
  color: #303133;
}

.coming-soon p {
  color: #909399;
  margin: 0;
}
</style>
