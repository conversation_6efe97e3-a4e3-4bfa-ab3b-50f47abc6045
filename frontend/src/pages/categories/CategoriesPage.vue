<template>
  <div class="categories-page">
    <div class="page-header">
      <div class="header-content">
        <div>
          <h1 class="page-title">分类管理</h1>
          <p class="page-subtitle">管理小说分类，组织您的小说收藏</p>
        </div>
        <el-button type="primary" :icon="Plus" @click="showCreateDialog">
          新建分类
        </el-button>
      </div>
    </div>

    <!-- 分类列表 -->
    <el-card>
      <div v-loading="loading" class="categories-content">
        <div v-if="categories.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无分类数据">
            <el-button type="primary" :icon="Plus" @click="showCreateDialog">
              创建第一个分类
            </el-button>
          </el-empty>
        </div>

        <div v-else class="categories-grid">
          <div
            v-for="category in categories"
            :key="category.id"
            class="category-card"
          >
            <div class="category-header">
              <div class="category-icon">
                <el-icon><Folder /></el-icon>
              </div>
              <div class="category-info">
                <h3 class="category-name">{{ category.name }}</h3>
                <p class="category-description">{{ category.description || '暂无描述' }}</p>
              </div>
            </div>

            <div class="category-stats">
              <div class="stat-item">
                <span class="stat-label">小说数量</span>
                <span class="stat-value">{{ category.novelsCount || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">排序权重</span>
                <span class="stat-value">{{ category.sortOrder }}</span>
              </div>
            </div>

            <div class="category-actions">
              <el-button size="small" :icon="Edit" @click="editCategory(category)">
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                :icon="Delete"
                @click="deleteCategory(category)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 创建/编辑分类对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEditing ? '编辑分类' : '新建分类'"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入分类名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="分类描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入分类描述（可选）"
            :rows="3"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="排序权重" prop="sortOrder">
          <el-input-number
            v-model="form.sortOrder"
            :min="0"
            :max="9999"
            placeholder="数值越小排序越靠前"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="submitForm">
          {{ isEditing ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Edit, Delete, Folder } from '@element-plus/icons-vue'
import { useCategoriesStore } from '@/stores/categories'
import type { Category, CategoryForm } from '@/types/models'

const categoriesStore = useCategoriesStore()

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const isEditing = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()
const currentCategory = ref<Category | null>(null)

// 表单数据
const form = reactive<CategoryForm>({
  name: '',
  description: '',
  sortOrder: 0
})

// 表单验证规则
const formRules: FormRules<CategoryForm> = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 100, message: '分类名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 1000, message: '分类描述长度不能超过 1000 个字符', trigger: 'blur' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序权重', trigger: 'blur' },
    { type: 'number', min: 0, max: 9999, message: '排序权重必须在 0 到 9999 之间', trigger: 'blur' }
  ]
}

// 计算属性
const categories = computed(() => categoriesStore.categories)

// 方法
const loadCategories = async () => {
  loading.value = true
  try {
    await categoriesStore.fetchCategories({ sort: 'sortOrder', order: 'asc' })
  } catch (error) {
    ElMessage.error('加载分类列表失败')
  } finally {
    loading.value = false
  }
}

const showCreateDialog = () => {
  isEditing.value = false
  currentCategory.value = null
  resetForm()
  dialogVisible.value = true
}

const editCategory = (category: Category) => {
  isEditing.value = true
  currentCategory.value = category
  form.name = category.name
  form.description = category.description || ''
  form.sortOrder = category.sortOrder
  dialogVisible.value = true
}

const resetForm = () => {
  form.name = ''
  form.description = ''
  form.sortOrder = 0
  formRef.value?.clearValidate()
}

const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    if (isEditing.value && currentCategory.value) {
      await categoriesStore.updateCategory(currentCategory.value.id, form)
      ElMessage.success('分类更新成功')
    } else {
      await categoriesStore.createCategory(form)
      ElMessage.success('分类创建成功')
    }

    dialogVisible.value = false
  } catch (error) {
    console.error('提交表单失败:', error)
  } finally {
    submitting.value = false
  }
}

const deleteCategory = async (category: Category) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分类"${category.name}"吗？如果分类下有小说，将一并删除。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await categoriesStore.deleteCategory(category.id, true)
    ElMessage.success('分类删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
    }
  }
}

// 生命周期
onMounted(() => {
  loadCategories()
})
</script>

<style scoped>
.categories-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #909399;
  margin: 0;
}

.categories-content {
  min-height: 200px;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.category-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: white;
  transition: all 0.3s;
}

.category-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
}

.category-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.category-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
}

.category-info {
  flex: 1;
  min-width: 0;
}

.category-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
  word-break: break-word;
}

.category-description {
  font-size: 13px;
  color: #909399;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.category-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.category-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .categories-grid {
    grid-template-columns: 1fr;
  }

  .category-stats {
    justify-content: space-around;
  }

  .category-actions {
    justify-content: center;
  }
}
</style>
