<template>
  <div class="novels-page">
    <div class="page-header">
      <div class="header-content">
        <div>
          <h1 class="page-title">小说管理</h1>
          <p class="page-subtitle">管理您的小说收藏，记录阅读进度</p>
        </div>
        <el-button type="primary" :icon="Plus" @click="showCreateDialog">
          添加小说
        </el-button>
      </div>
    </div>

    <!-- 筛选工具栏 -->
    <el-card class="filter-card">
      <div class="filter-content">
        <div class="filter-left">
          <el-select
            v-model="filters.categoryId"
            placeholder="选择分类"
            clearable
            style="width: 150px"
            @change="loadNovels"
          >
            <el-option
              v-for="category in categoriesOptions"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            />
          </el-select>

          <el-select
            v-model="filters.status"
            placeholder="阅读状态"
            clearable
            style="width: 120px"
            @change="loadNovels"
          >
            <el-option label="未开始" value="not_started" />
            <el-option label="阅读中" value="reading" />
            <el-option label="已完成" value="completed" />
            <el-option label="暂停" value="paused" />
          </el-select>
        </div>

        <div class="filter-right">
          <el-input
            v-model="filters.search"
            placeholder="搜索小说标题或作者"
            :prefix-icon="Search"
            clearable
            style="width: 250px"
            @keyup.enter="loadNovels"
            @clear="loadNovels"
          />
          <el-button :icon="Search" @click="loadNovels">搜索</el-button>
        </div>
      </div>
    </el-card>

    <!-- 小说列表 -->
    <el-card>
      <div v-loading="loading" class="novels-content">
        <div v-if="novels.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无小说数据">
            <el-button type="primary" :icon="Plus" @click="showCreateDialog">
              添加第一本小说
            </el-button>
          </el-empty>
        </div>

        <div v-else class="novels-grid">
          <div
            v-for="novel in novels"
            :key="novel.id"
            class="novel-card"
            @click="viewNovel(novel)"
          >
            <div class="novel-cover">
              <img
                v-if="novel.coverImage"
                :src="novel.coverImage"
                :alt="novel.title"
                class="cover-image"
              />
              <div v-else class="cover-placeholder">
                <el-icon><Reading /></el-icon>
              </div>
            </div>

            <div class="novel-info">
              <h3 class="novel-title">{{ novel.title }}</h3>
              <p class="novel-author">{{ novel.author }}</p>
              <p class="novel-description">{{ novel.description || '暂无简介' }}</p>

              <div class="novel-meta">
                <el-tag :type="getStatusType(novel.readingStatus)" size="small">
                  {{ getStatusLabel(novel.readingStatus) }}
                </el-tag>
                <span class="records-count">{{ novel.recordsCount }} 条记录</span>
              </div>

              <div class="novel-actions" @click.stop>
                <el-button size="small" :icon="View" @click="viewNovel(novel)">
                  查看
                </el-button>
                <el-button size="small" :icon="Edit" @click="editNovel(novel)">
                  编辑
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  :icon="Delete"
                  @click="deleteNovel(novel)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="novels.length > 0" class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadNovels"
            @current-change="loadNovels"
          />
        </div>
      </div>
    </el-card>

    <!-- 创建/编辑小说对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEditing ? '编辑小说' : '添加小说'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="小说标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入小说标题"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="作者" prop="author">
          <el-input
            v-model="form.author"
            placeholder="请输入作者姓名"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="分类" prop="categoryId">
          <el-select
            v-model="form.categoryId"
            placeholder="请选择分类"
            style="width: 100%"
          >
            <el-option
              v-for="category in categoriesOptions"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="简介" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入小说简介（可选）"
            :rows="4"
            maxlength="5000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="阅读状态" prop="readingStatus">
          <el-select v-model="form.readingStatus" style="width: 100%">
            <el-option label="未开始" value="not_started" />
            <el-option label="阅读中" value="reading" />
            <el-option label="已完成" value="completed" />
            <el-option label="暂停" value="paused" />
          </el-select>
        </el-form-item>

        <el-form-item label="个人评分" prop="personalRating">
          <el-rate v-model="form.personalRating" :max="5" show-text />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="submitForm">
          {{ isEditing ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Search, View, Edit, Delete, Reading } from '@element-plus/icons-vue'
import { useNovelsStore } from '@/stores/novels'
import { useCategoriesStore } from '@/stores/categories'
import type { Novel, NovelForm, ReadingStatus } from '@/types/models'

const router = useRouter()
const novelsStore = useNovelsStore()
const categoriesStore = useCategoriesStore()

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const isEditing = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()
const currentNovel = ref<Novel | null>(null)

// 筛选条件
const filters = reactive({
  categoryId: undefined as number | undefined,
  status: '',
  search: ''
})

// 表单数据
const form = reactive<NovelForm>({
  categoryId: undefined,
  title: '',
  author: '',
  description: '',
  coverImage: '',
  tags: [],
  readingStatus: 'not_started' as ReadingStatus,
  personalRating: undefined
})

// 表单验证规则
const formRules: FormRules<NovelForm> = {
  title: [
    { required: true, message: '请输入小说标题', trigger: 'blur' },
    { min: 1, max: 200, message: '小说标题长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  author: [
    { required: true, message: '请输入作者姓名', trigger: 'blur' },
    { min: 1, max: 100, message: '作者姓名长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  description: [
    { max: 5000, message: '小说简介长度不能超过 5000 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const novels = computed(() => novelsStore.novels)
const pagination = computed(() => novelsStore.pagination)
const categoriesOptions = computed(() => categoriesStore.categoriesOptions)

// 方法
const loadNovels = async () => {
  loading.value = true
  try {
    await novelsStore.fetchNovels({
      page: pagination.value.current,
      size: pagination.value.size,
      categoryId: filters.categoryId,
      search: filters.search,
      status: filters.status,
      sort: 'createdAt',
      order: 'desc'
    })
  } catch (error) {
    ElMessage.error('加载小说列表失败')
  } finally {
    loading.value = false
  }
}

const showCreateDialog = () => {
  isEditing.value = false
  currentNovel.value = null
  resetForm()
  dialogVisible.value = true
}

const viewNovel = (novel: Novel) => {
  router.push(`/novels/${novel.id}`)
}

const editNovel = (novel: Novel) => {
  isEditing.value = true
  currentNovel.value = novel
  form.categoryId = novel.categoryId
  form.title = novel.title
  form.author = novel.author
  form.description = novel.description || ''
  form.coverImage = novel.coverImage || ''
  form.tags = novel.tags || []
  form.readingStatus = novel.readingStatus
  form.personalRating = novel.personalRating
  dialogVisible.value = true
}

const resetForm = () => {
  form.categoryId = undefined
  form.title = ''
  form.author = ''
  form.description = ''
  form.coverImage = ''
  form.tags = []
  form.readingStatus = 'not_started'
  form.personalRating = undefined
  formRef.value?.clearValidate()
}

const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    if (isEditing.value && currentNovel.value) {
      await novelsStore.updateNovel(currentNovel.value.id, form)
      ElMessage.success('小说更新成功')
    } else {
      await novelsStore.createNovel(form)
      ElMessage.success('小说添加成功')
    }

    dialogVisible.value = false
    loadNovels()
  } catch (error) {
    console.error('提交表单失败:', error)
  } finally {
    submitting.value = false
  }
}

const deleteNovel = async (novel: Novel) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除小说"${novel.title}"吗？相关的分析记录也将一并删除。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await novelsStore.deleteNovel(novel.id, true)
    ElMessage.success('小说删除成功')
    loadNovels()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除小说失败:', error)
    }
  }
}

const getStatusType = (status: ReadingStatus): string => {
  const typeMap = {
    'not_started': 'info',
    'reading': 'warning',
    'completed': 'success',
    'paused': 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusLabel = (status: ReadingStatus): string => {
  const labelMap = {
    'not_started': '未开始',
    'reading': '阅读中',
    'completed': '已完成',
    'paused': '暂停'
  }
  return labelMap[status] || '未知'
}

// 生命周期
onMounted(async () => {
  await categoriesStore.fetchCategories()
  loadNovels()
})
</script>

<style scoped>
.novels-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #909399;
  margin: 0;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.filter-left,
.filter-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.novels-content {
  min-height: 400px;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.novels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.novel-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  transition: all 0.3s;
  cursor: pointer;
}

.novel-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
  transform: translateY(-2px);
}

.novel-cover {
  height: 200px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-placeholder {
  font-size: 48px;
  color: #c0c4cc;
}

.novel-info {
  padding: 16px;
}

.novel-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.novel-author {
  font-size: 14px;
  color: #606266;
  margin: 0 0 8px 0;
}

.novel-description {
  font-size: 13px;
  color: #909399;
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.novel-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.records-count {
  font-size: 12px;
  color: #909399;
}

.novel-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filter-content {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-left,
  .filter-right {
    justify-content: center;
  }

  .novels-grid {
    grid-template-columns: 1fr;
  }

  .novel-actions {
    justify-content: center;
  }
}
</style>
